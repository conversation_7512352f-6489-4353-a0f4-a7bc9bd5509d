//
//  ProductsCertificationViewController.m
//  Nano_Loan
//
//  Created by yongsheng ye on 2025/6/23.
//
//  产品详情页-提单

#import "ProductsCertificationViewController.h"
#import "NetworkManager.h"
#import <MBProgressHUD/MBProgressHUD.h>
#import "ProductInfo.h"
#import "TrustItem.h"
#import "IdCardAuthenticationViewController.h"
#import <SafariServices/SafariServices.h>
#import "H5WebViewController.h"
#import "PersonalInformationAuthenticationViewController.h"
#import "JobAuthenticationViewController.h"
#import "AddressBookAuthenticationViewController.h"
#import <Masonry/Masonry.h>
#import "RiskEventManager.h"
#import <SDWebImage/UIImageView+WebCache.h>


@interface ProductsCertificationViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) ProductInfo *productInfo;
@property (nonatomic, strong) NSArray<TrustItem *> *trustItems;
@property (nonatomic, copy) NSString *agreementURL;       // 协议链接
@property (nonatomic, strong) TrustItem *nextPendingItem; // 接口 isemily 指定的下一待办认证项

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *applyButton;
@property (nonatomic, strong) UIView *navView; // 自定义导航栏
@property (nonatomic, strong) UIButton *agreementButton; // 协议展示按钮

@end

@implementation ProductsCertificationViewController {
    BOOL _didInitialLoad;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加透明背景图
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    // 自定义导航栏
    [self setupCustomNavBar];
    [self setupApplyButton];
    [self setupProtocolButton];
    [self setupTableView];
    [self requestTopmost];
    _didInitialLoad = YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    // 隐藏系统导航栏，使用自定义导航
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    if (_didInitialLoad) {
        [self requestTopmost]; // Refresh every time when returning
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    // 恢复系统导航栏
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

#pragma mark - 自定义导航栏
- (void)setupCustomNavBar {
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];
    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [backBtn setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
    backBtn.frame = CGRectMake(0, 0, 34, 34);
    backBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:backBtn];
    [NSLayoutConstraint activateConstraints:@[
        [backBtn.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [backBtn.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [backBtn.widthAnchor constraintEqualToConstant:34],
        [backBtn.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Identity Authentication";
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];
}

- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // 隐藏滚动条
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.tableView];

    [NSLayoutConstraint activateConstraints:@[
        [self.tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:12],
        [self.tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-12],
        [self.tableView.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:self.agreementButton.topAnchor constant:-12]
    ]];
}

- (void)setupApplyButton {
    self.applyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    //按钮文字颜色为白色
    [self.applyButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.applyButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.applyButton setTitle:@"Go" forState:UIControlStateNormal];
    self.applyButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22.0f];
    [self.applyButton.heightAnchor constraintEqualToConstant:48].active = YES;
    [self.applyButton addTarget:self action:@selector(applyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.applyButton setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateNormal];
    [self.view addSubview:self.applyButton];

    [NSLayoutConstraint activateConstraints:@[
        [self.applyButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [self.applyButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],
        [self.applyButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-12]
    ]];
}

#pragma mark - 协议按钮
- (void)setupProtocolButton {
    self.agreementButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.agreementButton.translatesAutoresizingMaskIntoConstraints = NO;

    // 创建富文本，"Please read the Loan Agreement" 其中 "Loan Agreement" 为蓝色高亮
    NSString *fullText = @"Please read the Loan Agreement";
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:fullText];

    // 设置整体文本样式
    [attributedText addAttribute:NSFontAttributeName
                           value:[UIFont systemFontOfSize:14.0]
                           range:NSMakeRange(0, fullText.length)];
    [attributedText addAttribute:NSForegroundColorAttributeName
                           value:[UIColor whiteColor]
                           range:NSMakeRange(0, fullText.length)];

    // 设置 "Loan Agreement" 为蓝色高亮
    NSRange loanAgreementRange = [fullText rangeOfString:@"Loan Agreement"];
    if (loanAgreementRange.location != NSNotFound) {
        [attributedText addAttribute:NSForegroundColorAttributeName
                               value:[UIColor systemBlueColor]
                               range:loanAgreementRange];
    }

    [self.agreementButton setAttributedTitle:attributedText forState:UIControlStateNormal];
    [self.agreementButton addTarget:self action:@selector(agreementButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.agreementButton];

    [NSLayoutConstraint activateConstraints:@[
        [self.agreementButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.agreementButton.heightAnchor constraintEqualToConstant:20],
        [self.agreementButton.bottomAnchor constraintEqualToAnchor:self.applyButton.topAnchor constant:-8]
    ]];
}

#pragma mark - API Request

- (void)requestTopmost {
    if (!self.splendid || self.splendid.length == 0) {
        return;
    }
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Loading…";

    NSDictionary *params = @{ @"splendid": self.splendid ?: @"" };
    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/topmost" params:params completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];

            // 统一提示：无论成功或失败，仅展示接口返回的消息（或错误描述）
            NSString *toastMsg = error ? (error.localizedDescription ?: @"Error") : (response[@"patted"] ?: @"");
//            if (toastMsg.length > 0) {
//                MBProgressHUD *toast = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
//                toast.mode = MBProgressHUDModeText;
//                toast.label.text = toastMsg;
//                [toast hideAnimated:YES afterDelay:1.5];
//            }

            if (error) {
                return; // 发生网络错误，后续 UI 数据无法更新
            }
            NSDictionary *awkward = response[@"awkward"];
            if (![awkward isKindOfClass:[NSDictionary class]]) return;
            NSDictionary *insurprise = awkward[@"insurprise"];
            weakSelf.productInfo = [[ProductInfo alloc] initWithDictionary:insurprise];
            // 更新底部按钮文案，根据接口 embroidery 字段
            NSString *btnTitle = weakSelf.productInfo.applyLabel.length > 0 ? weakSelf.productInfo.applyLabel : @"Go";
            [weakSelf.applyButton setTitle:btnTitle forState:UIControlStateNormal];
            NSArray *trustArray = awkward[@"trust"];
            NSMutableArray *tmp = [NSMutableArray array];
            for (NSDictionary *itemDict in trustArray) {
                [tmp addObject:[[TrustItem alloc] initWithDictionary:itemDict]];
            }
            weakSelf.trustItems = [tmp copy];
            // 解析 isemily 字段，确定后端指定的下一步认证项（若存在）
            NSDictionary *isemily = awkward[@"isemily"];
            if ([isemily isKindOfClass:[NSDictionary class]] && [isemily[@"shookhands"] length] > 0) {
                weakSelf.nextPendingItem = [[TrustItem alloc] initWithDictionary:isemily];
            } else {
                weakSelf.nextPendingItem = nil; // 为空代表全部已完成
            }
            [weakSelf buildTableHeader];
            [weakSelf.tableView reloadData];
            NSDictionary *pickles = awkward[@"pickles"];
            if ([pickles isKindOfClass:[NSDictionary class]]) {
                weakSelf.agreementURL = pickles[@"eating"] ?: @"";
            } else {
                weakSelf.agreementURL = @"";
            }

            // 根据是否有协议链接决定按钮显示
            weakSelf.agreementButton.hidden = (weakSelf.agreementURL.length == 0);

            // 此处不再额外弹出固定文案提示，已在上方统一展示接口返回的消息
        });
    }];
}

- (void)buildTableHeader {
    if (!self.productInfo) return;

    // 先确保 tableView 已经布局完成，获取其准确的宽度
    [self.tableView layoutIfNeeded];
    CGFloat headerWidth = CGRectGetWidth(self.tableView.frame);
    // 如果此时拿不到宽度（极端情况），兜底按屏幕宽度-24 处理
    if (headerWidth <= 0) {
        headerWidth = [UIScreen mainScreen].bounds.size.width - 24.0;
    }
    // 设计稿 351 * 170，根据宽度等比缩放高度，保持图片不变形
    CGFloat bgHeight = headerWidth * 170.0 / 351.0;

    // 头部容器视图，宽度=tableView 宽度，高度=计算后的等比高度
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, headerWidth, bgHeight)];

        // 背景图填充整个 header，保持等比缩放
    UIImageView *bgImg = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, headerWidth, bgHeight)];
    bgImg.image = [UIImage imageNamed:@"product_bg"];
    bgImg.contentMode = UIViewContentModeScaleAspectFill;
    bgImg.layer.cornerRadius = 20;
    bgImg.layer.masksToBounds = YES;
    [header addSubview:bgImg];

    // 文本内容
    CGFloat padding = 16.0;
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:18.0f];
    nameLabel.textColor = [UIColor whiteColor];
    nameLabel.text = @"Loan Information";
    [header addSubview:nameLabel];
    nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [nameLabel.leadingAnchor constraintEqualToAnchor:header.leadingAnchor constant:64],
        [nameLabel.topAnchor constraintEqualToAnchor:header.topAnchor constant:12]
    ]];

    //广告词-固定
    UILabel *adLabel = [[UILabel alloc] init];
    // 使用接口下发的产品金额文案（heardgwendoline）
    adLabel.text = self.productInfo.loanAmountLabel.length > 0 ? self.productInfo.loanAmountLabel : @"Maximum limited";
    adLabel.textAlignment = NSTextAlignmentCenter;
    adLabel.font = [UIFont systemFontOfSize:13.0f];
    adLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [header addSubview:adLabel];
    [adLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(header).offset(36);
        make.height.mas_equalTo(13);
        make.centerY.equalTo(header).offset(-10);
    }];

    //顶部产品价格
    UILabel *priceLabel = [[UILabel alloc] init];
    NSString *teamAmount = self.productInfo.team;
    NSNumber *firstAmount = self.productInfo.amountOptions.firstObject;
    if (teamAmount.length > 0) {
        priceLabel.text = [NSString stringWithFormat:@"%@%@", self.productInfo.symbol ?: @"", teamAmount];
    } else if ([firstAmount isKindOfClass:[NSNumber class]]) {
        priceLabel.text = [NSString stringWithFormat:@"%@%.0f", self.productInfo.symbol ?: @"", [firstAmount doubleValue]];
    } else if ([firstAmount isKindOfClass:[NSString class]]) {
        priceLabel.text = [NSString stringWithFormat:@"%@%@", self.productInfo.symbol ?: @"", (NSString *)firstAmount];
    }
    priceLabel.textAlignment = NSTextAlignmentCenter;
    priceLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:30.0f];
    priceLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [header addSubview:priceLabel];
    [priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(header).offset(36);
        make.height.mas_equalTo(37);
        make.top.equalTo(adLabel.mas_bottom).offset(6);
    }];

    // //右侧 logo
    UIImageView *rightLogoImgView = [[UIImageView alloc] init];
    // 使用网络图片加载产品 logo，tapestrywork 字段映射到 self.productInfo.imageURL
    NSURL *logoURL = [NSURL URLWithString:self.productInfo.imageURL ?: @""];
    UIImage *placeholderImage = [UIImage imageNamed:@"home_product_logo_x_r"];
    [rightLogoImgView sd_setImageWithURL:logoURL
                        placeholderImage:placeholderImage
                                 options:0
                                progress:nil
                               completed:nil];
    rightLogoImgView.contentMode = UIViewContentModeScaleAspectFill;
    rightLogoImgView.clipsToBounds = YES;
    [header addSubview:rightLogoImgView];
    [rightLogoImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(header).offset(-34);
        make.width.mas_equalTo(25);
        make.height.mas_equalTo(25);
        make.top.equalTo(adLabel.mas_top);
    }];

    // //产品名称
    UILabel *productNameLabel = [[UILabel alloc] init];
    productNameLabel.text = self.productInfo.name;
    productNameLabel.textAlignment = NSTextAlignmentCenter;
    productNameLabel.font = [UIFont systemFontOfSize:12.0f];
    productNameLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [header addSubview:productNameLabel];
    [productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(rightLogoImgView.mas_right);
        make.height.mas_equalTo(13);
        make.centerY.equalTo(priceLabel);
    }];

    self.tableView.tableHeaderView = header;
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    // 所有cell都放到sectionHeaderView，cell数量为0
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    // 浮岛卡片容器
    CGFloat cardWidth = self.view.bounds.size.width - 24; // 左右各12pt
    CGFloat noticeHeight = 50.0;
    CGFloat spacing = 10.0; // 通知 cell 与首个认证 cell 以及认证 cell 之间的间距

    // 动态计算认证 cell 高度：设计稿参考 innerWidth 335 ⇢ height 91
    CGFloat innerWidth = cardWidth - 16.0; // authCell 实际宽度
    CGFloat cellHeight = innerWidth * 91.0 / 335.0;

    // 计算总高度：通知 cell + 间距 + 认证 cell（含间距） + 上下内边距 24
    CGFloat totalHeight = noticeHeight + (self.trustItems.count * (cellHeight + spacing)) + 24; // 当 trustItems.count 为 0 时自动忽略间距
    UIView *cardView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, cardWidth, totalHeight)];
    cardView.backgroundColor = [UIColor colorWithRed:169/255.0 green:201/255.0 blue:255/255.0 alpha:1.0]; // #A9C9FF
    cardView.layer.cornerRadius = 16;
    cardView.layer.masksToBounds = YES;
    // 通知cell
    UIView *noticeCell = [[UIView alloc] initWithFrame:CGRectMake(8, 12, cardWidth-16, noticeHeight)]; // 内部再缩进8pt
    noticeCell.backgroundColor = [UIColor clearColor];
    noticeCell.layer.cornerRadius = 12;
    noticeCell.layer.masksToBounds = YES;
    UIImageView *noticeBg = [[UIImageView alloc] initWithFrame:noticeCell.bounds];
    noticeBg.image = [UIImage imageNamed:@"cell_bg_notice"];
    noticeBg.contentMode = UIViewContentModeScaleToFill;
    noticeBg.layer.cornerRadius = 12;
    noticeBg.layer.masksToBounds = YES;
    [noticeCell addSubview:noticeBg];
    // 可添加通知icon/label等内容
    [cardView addSubview:noticeCell];
    // 认证项cell
    for (NSInteger i = 0; i < self.trustItems.count; i++) {
        TrustItem *item = self.trustItems[i];
        CGFloat y = 12 + noticeHeight + spacing + (i * (cellHeight + spacing));
        UIView *authCell = [[UIView alloc] initWithFrame:CGRectMake(8, y, cardWidth-16, cellHeight)]; // 内部再缩进8pt
        authCell.backgroundColor = [UIColor clearColor];
        authCell.layer.cornerRadius = 12;
        authCell.layer.masksToBounds = YES;
        // 背景图改为接口返回的 iconURL（sallyhope 字段），无则回退到本地默认图
        UIImageView *bgImg = [[UIImageView alloc] initWithFrame:authCell.bounds];
        UIImage *placeholder = [UIImage imageNamed:item.completed ? @"cell_bg_identity_verified" : @"cell_bg_identity_unverified"];
        if (item.iconURL.length > 0) {
            [bgImg sd_setImageWithURL:[NSURL URLWithString:item.iconURL]
                       placeholderImage:placeholder
                                options:0
                               progress:nil
                              completed:nil];
        } else {
            bgImg.image = placeholder;
        }
        bgImg.contentMode = UIViewContentModeScaleToFill;
        bgImg.layer.cornerRadius = 12;
        bgImg.layer.masksToBounds = YES;
        [authCell addSubview:bgImg];

        // Cell 上不再额外叠加文字，图片已包含标题
        // 点击手势
        authCell.userInteractionEnabled = YES;
        authCell.tag = 100 + i;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(authCellTapped:)];
        [authCell addGestureRecognizer:tap];
        [cardView addSubview:authCell];
    }
    return cardView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    // 动态计算认证 cell 高度，保持与 viewForHeaderInSection 一致
    CGFloat cardWidth = self.view.bounds.size.width - 24;
    CGFloat innerWidth = cardWidth - 16.0;
    CGFloat cellHeight = innerWidth * 91.0 / 335.0;
    return 50 + (self.trustItems.count * (cellHeight + 10)) + 24; // 50 为 notice cell，高度含间距
}

#pragma mark - 顺序控制辅助
/// 返回首个未完成认证项的索引，若全部完成则返回 NSNotFound
- (NSUInteger)firstIncompleteIndex {
    for (NSUInteger idx = 0; idx < self.trustItems.count; idx++) {
        TrustItem *item = self.trustItems[idx];
        if (!item.completed) { return idx; }
    }
    return NSNotFound;
}

/// 返回首个未完成认证项，若全部完成则返回 nil
- (TrustItem *)firstIncompleteItem {
    // 第一层：使用接口 isemily 指定的下一待办项
    if (self.nextPendingItem && self.nextPendingItem.type.length > 0) {
        // 在已加载的列表中查找对应类型，保持数据一致
        for (TrustItem *item in self.trustItems) {
            if ([item.type isEqualToString:self.nextPendingItem.type]) {
                return item;
            }
        }
        // 若列表中没有（例如动态扩展类型），直接返回 isemily 生成的对象
        return self.nextPendingItem;
    }

    // 第二层：本地遍历寻找首个未完成项（作为兜底）
    NSUInteger idx = [self firstIncompleteIndex];
    return idx != NSNotFound ? self.trustItems[idx] : nil;
}

// 点击认证项cell
- (void)authCellTapped:(UITapGestureRecognizer *)tap {
    NSInteger idx = tap.view.tag - 100;
    if (idx < 0 || idx >= self.trustItems.count) { return; }
    TrustItem *clickedItem = self.trustItems[idx];
    // 若已完成则直接进入编辑；若未完成则只能跳转到首个未完成项
    if (!clickedItem.completed) {
        TrustItem *nextItem = [self firstIncompleteItem];
        if (nextItem) {
            clickedItem = nextItem; // 强制顺序跳转
        }
    }
    [self handleTrustItemTap:clickedItem];
}

- (void)handleTrustItemTap:(TrustItem *)item {
    BOOL isEdit = item.completed;
    if (item.title && [item.title.lowercaseString containsString:@"authentication"]) {
        IdCardAuthenticationViewController *idVC = [[IdCardAuthenticationViewController alloc] init];
        idVC.editMode = isEdit;
        idVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:idVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"personal information"]) {
        PersonalInformationAuthenticationViewController *pVC = [[PersonalInformationAuthenticationViewController alloc] init];
        pVC.editMode = isEdit;
        pVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:pVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"job information"]) {
        JobAuthenticationViewController *jobVC = [[JobAuthenticationViewController alloc] init];
        jobVC.editMode = isEdit;
        jobVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:jobVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"contact information"]) {
        AddressBookAuthenticationViewController *abVC = [[AddressBookAuthenticationViewController alloc] init];
        abVC.editMode = isEdit;
        abVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:abVC animated:YES];
        return;
    }
    if ([item.link containsString:@"http"]) {
        NSString *urlStr = item.link;
        if (self.productInfo.productId.length > 0) {
            NSURLComponents *components = [NSURLComponents componentsWithString:urlStr];
            NSMutableArray<NSURLQueryItem *> *items = components.queryItems ? [components.queryItems mutableCopy] : [NSMutableArray array];
            BOOL hasSplendid = NO;
            for (NSURLQueryItem *qi in items) {
                if ([qi.name isEqualToString:@"splendid"]) { hasSplendid = YES; break; }
            }
            if (!hasSplendid) {
                [items addObject:[NSURLQueryItem queryItemWithName:@"splendid" value:self.productInfo.productId]];
                components.queryItems = items;
            }
            urlStr = components.URL.absoluteString;
        }
        H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:urlStr];
        [self.navigationController pushViewController:webVC animated:YES];
    }
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    TrustItem *item = self.trustItems[indexPath.row-1];
    BOOL isEdit = item.completed;
    if (item.title && [item.title.lowercaseString containsString:@"authentication"]) {
        IdCardAuthenticationViewController *idVC = [[IdCardAuthenticationViewController alloc] init];
        idVC.editMode = isEdit;
        idVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:idVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"personal information"]) {
        PersonalInformationAuthenticationViewController *pVC = [[PersonalInformationAuthenticationViewController alloc] init];
        pVC.editMode = isEdit;
        pVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:pVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"job information"]) {
        JobAuthenticationViewController *jobVC = [[JobAuthenticationViewController alloc] init];
        jobVC.editMode = isEdit;
        jobVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:jobVC animated:YES];
        return;
    } else if (item.title && [item.title.lowercaseString containsString:@"contact information"]) {
        AddressBookAuthenticationViewController *abVC = [[AddressBookAuthenticationViewController alloc] init];
        abVC.editMode = isEdit;
        abVC.splendid = self.productInfo.productId;
        [self.navigationController pushViewController:abVC animated:YES];
        return;
    }

    if ([item.link containsString:@"http"]) {
        NSString *urlStr = item.link;
        if (self.productInfo.productId.length > 0) {
            NSURLComponents *components = [NSURLComponents componentsWithString:urlStr];
            NSMutableArray<NSURLQueryItem *> *items = components.queryItems ? [components.queryItems mutableCopy] : [NSMutableArray array];
            BOOL hasSplendid = NO;
            for (NSURLQueryItem *qi in items) {
                if ([qi.name isEqualToString:@"splendid"]) { hasSplendid = YES; break; }
            }
            if (!hasSplendid) {
                [items addObject:[NSURLQueryItem queryItemWithName:@"splendid" value:self.productInfo.productId]];
                components.queryItems = items;
            }
            urlStr = components.URL.absoluteString;
        }
        H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:urlStr];
        [self.navigationController pushViewController:webVC animated:YES];
    }
}

#pragma mark - Actions

- (void)applyButtonTapped {
    // 若有未完成认证，则引导用户先完成认证流程
    TrustItem *nextItem = [self firstIncompleteItem];
    if (nextItem) {
        [self handleTrustItemTap:nextItem];
        return;
    }

    if (!self.productInfo) { return; }
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Submitting…";
    NSDictionary *params = @{
        @"patiently": self.productInfo.orderNumber ?: @"",
        @"team": self.productInfo.team ?: @"",
        @"twowomen": self.productInfo.termValue ?: @"",
        @"shaggier": self.productInfo.termType ?: @""
    };
    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/tarts" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];

            // 统一提示：直接展示接口返回的消息（或错误描述）
            NSString *toastMsg = error ? (error.localizedDescription ?: @"Error") : (response[@"patted"] ?: @"");
            if (toastMsg.length > 0) {
                MBProgressHUD *toast = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                toast.mode = MBProgressHUDModeText;
                toast.label.text = toastMsg;
                [toast hideAnimated:YES afterDelay:1.5];
            }

            if (error) {
                return; // 网络错误，无法继续后续流程
            }
            NSDictionary *awkward = response[@"awkward"];
            NSString *urlStr = [awkward isKindOfClass:[NSDictionary class]] ? awkward[@"toteach"] : nil;
            if (urlStr.length > 0) {
                // 此处已在上方统一展示接口返回消息，无需额外弹窗

                // 埋点9：开始审贷，startTime=endTime=当前
                NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
                NSLog(@"🚀 [埋点9-开始审贷] 开始上报 startTime:%.0f endTime:%.0f orderId:%@", now, now, weakSelf.productInfo.orderNumber);
                [RiskEventManager reportEventType:RiskEventTypeStartLoan startTime:now endTime:now orderId:weakSelf.productInfo.orderNumber];
                UINavigationController *nav = weakSelf.navigationController;
                H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:urlStr];
                // 先回到首页（栈底 VC）再推送 H5，避免返回时落到产品详情页
                [nav popToRootViewControllerAnimated:NO];
                [nav pushViewController:webVC animated:YES];
            } else {
                // 若服务端未返回跳转链接，仍然由上方 toast 显示原因
            }
        });
    }];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)agreementButtonTapped {
    if (self.agreementURL.length > 0) {
        H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:self.agreementURL];
        [self.navigationController pushViewController:webVC animated:YES];
    }
}

// 新增方法：设置背景图
- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}


/*
 缺少一个顺序控制器,已完成选项支持编辑,点哪个都跳哪个
 未完成选项只能按照顺序来,如完成了第2项,点击第4项,则会跳转第3项,下方go按钮也是这个逻辑,在未完成全部认证前.点击go是去到接口下发的最后一项认证页.
 确保埋点获取顺序是正确的.
 
 
 
TODO:
1. 产品信息区内容布局可根据设计稿继续细化（如金额、产品名、icon等）。
2. cell高度、圆角、阴影等视觉细节可根据实际UI效果微调。
3. 若需支持横屏或iPad，需增加自适应布局。
4. 若后续有更多认证类型，需在cell背景图命名和切换逻辑中补充。
5. 其他未尽细节可根据实际需求补充。
*/



//产品详情认证项目列表
//原版
//'"shookhands":"public"', // ocr认证
//'"shookhands":"personal"', // 个人信息
//'"shookhands":"job"', // 个人信息
//'"shookhands":"ext"', // 紧急联系人
//'"shookhands":"bank"', // 绑卡
//混淆后
//'"shookhands":"angrily"',
//'"shookhands":"something"',
//'"shookhands":"pretended"',
//'"shookhands":"Betty"',
//'"shookhands":"nudged"',
//认证项组件
//原版
//'"wasbetty":"enum"',  //单选
//'"wasbetty":"txt"',   //输入框
//'"wasbetty":"citySelect"',   //城市选择器
//混淆后
//'"wasbetty":"always"',
//'"wasbetty":"tempered"',
//'"wasbetty":"hearing"',


/*
 {
 "awkward": {
   "inthat": 200,
   "insurprise": { //产品信息
     "fondly": [
       "1.100.000",
       "1.000.000"
     ], //额度范围
     "team": "1.100.000", //金额 【重要】
     "mput": [
       7
     ], //借款期限
     "heardgwendoline": "Jumlah Pinjaman(Rp)", //产品金额文案
     "fibs": "Jangka Pinjaman", //借款期限文案
     "darn": "1", //产品id【重要】
     "cando": "Super Prestamo", //产品名称【重要】
     "tapestrywork": "http://147.139.40.154:8818/yeahimg/ylogo.png", //产品LOGO【重要】
     "begnning": "*************45300522743", //订单号 风控埋点接口会用到【重要】
     "laceyor": 266561, //订单id
     "theselfishness": { // 遍历下方的数据，会包含期数和利率 【重要】
       "adored": {
           "coldchicken": "Loan Term",
           "witheveryone": "91 days"
       },
       "babyish": {
           "coldchicken": "Interest Rate",
           "witheveryone": "0.05%"
       }
     },
     "embroidery": "Isi Informasi", //底部按钮文案 【重要】
     "obviously": "",
     "twowomen": "91",
     "shaggier": 1,
     "toteach": "http://47.89.212.157:8080/www.google.com?productId=1",
     "biggerand": {
       "eyebrows": "+52 5525981300"
     },
     "intensly": "http://47.89.212.157:8080/#/complaint?productId=1"
   },
  
   "trust": [  //认证项  列表认证项顺序按照此字段值显示  后端可能会更改顺序
     {
        "coldchicken": "Informasi identitas", //标题 【重要】
           "replies": "Harap tingkatkan identitas", //【重要】
           "subject": 0,
           "toteach": "",
           "amused": 1,   //是否已完成 【重要】
           "listening": "Certification",
           "shookhands": "public 自定义的认证类型的值，参考接口文档左侧 7.值映射-产品详情认证项目列表",   //类型 【重要】用作判断, 混淆后参考值映射  根据该字段判断跳转对应页面
           "definite": 1,
           "thisto": 0,
           "expected": 1,
           "lake": "Silakan lengkapi sertifikasi secara berurutan",
           "sallyhope": "https://yinni-files-dev.oss-cn-shanghai.aliyuncs.com/commonpic/didentias.png" //logo 【重要】
     },
     {
       "coldchicken": "Informasi pribadi",
       "replies": "Harap tingkatkan informasi pribadi Anda",
       "subject": 0,
       "toteach": "",
       "amused": 1,
       "listening": "Sertifikasi",
       "shookhands": "personal 自定义的认证类型的值，参考接口文档左侧 7.值映射-产品详情认证项目列表",
       "definite": 1,
       "expected": 1,
       "lake": "Silakan lengkapi sertifikasi secara berurutan",
       "sallyhope": "https://yinni-files-dev.oss-cn-shanghai.aliyuncs.com/commonpic/dpribadi.png"
     },
     {
       "coldchicken": "Informasi pekerjaan",
       "replies": "Harap tingkatkan informasi pekerjaan",
       "subject": 0,
       "toteach": "",
       "amused": 1,
       "listening": "Sertifikasi",
       "shookhands": "job 自定义的认证类型的值，参考接口文档左侧 7.值映射-产品详情认证项目列表",
       "definite": 1,
       "expected": 1,
       "lake": "Silakan lengkapi sertifikasi secara berurutan",
       "sallyhope": "https://yinni-files-dev.oss-cn-shanghai.aliyuncs.com/commonpic/dpekerjaan.png"
     },
     {
       "coldchicken": "Kontak darurat",
       "replies": "Harap tingkatkan informasi kontak darurat",
       "subject": 0,
       "toteach": "",
       "amused": 1,
       "listening": "Sertifikasi",
       "shookhands": "ext 自定义的认证类型的值，参考接口文档左侧 7.值映射-产品详情认证项目列表",
       "definite": 1,
       "expected": 1,
       "lake": "Silakan lengkapi sertifikasi secara berurutan",
       "sallyhope": "https://yinni-files-dev.oss-cn-shanghai.aliyuncs.com/commonpic/dkontak.png"
     },
     {
       "coldchicken": "Informasi bank",
       "replies": "Harap tingkatkan informasi kartu bank Anda",
       "subject": 0,
       "toteach": "http://8.212.182.12:8834/#/EasyBk",  //【重要】 h5绑卡页面  有值时拼接公参跳转h5页面
       "amused": 0,
       "listening": "Sertifikasi",
       "shookhands": "bank 自定义的认证类型的值，参考接口文档左侧 7.值映射-产品详情认证项目列表",
       "definite": 1,
       "expected": 1,
       "lake": "Silakan lengkapi sertifikasi secara berurutan",
       "sallyhope": "https://yinni-files-dev.oss-cn-shanghai.aliyuncs.com/commonpic/dbank.png"
     }
   ],
   "isemily": {  //【重要】下一个待完成的认证项
       "shookhands": "bank", // 【重要】如果点击 "未认证" 的认证项，则跳转到 "这个字段下发的下一步" 的认证详情页，如果点击 "已完成" 的认证项，则打开点击当前的认证详情页。为空串代表全部认证项已完成
       "toteach": "http://8.212.182.12:8834/#/EasyBk",  //有值时 跳转对应的h5页面
       "subject": 0,
       "coldchicken": "Informasi bank"
   },
   "pickles": {   //【重要】借款协议
     "coldchicken": "Click to view the Loan Agreement",   // 【重要】展示文案  为空不显示
     "eating": "https://scb.britexfinancialspl.com/bee/yeah/frightente?putter=SBI2023070514464468362001"//【重要】跳转地址
   }
 },
 "modest": "0",
 "patted": "success"
 }
 
 */

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellId = @"TrustStepCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellId];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellId];
        cell.backgroundColor = [UIColor clearColor];
    }
    if (indexPath.row == 0) {
        // 通知装饰cell，只显示背景图
        cell.imageView.image = [UIImage imageNamed:@"cell_bg_notice"];
        cell.textLabel.text = @"";
        cell.detailTextLabel.text = @"";
    } else {
        TrustItem *item = self.trustItems[indexPath.row-1];
        NSDictionary *bgMap = @{
            @"angrily": item.completed ? @"cell_bg_identity_verified" : @"cell_bg_identity_unverified",
            @"something": item.completed ? @"cell_bg_personal_verified" : @"cell_bg_personal_unverified",
            @"pretended": item.completed ? @"cell_bg_job_verified" : @"cell_bg_job_unverified",
            @"Betty": item.completed ? @"cell_bg_contact_verified" : @"cell_bg_contact_unverified",
            @"nudged": item.completed ? @"cell_bg_bank_verified" : @"cell_bg_bank_unverified"
        };
        NSString *bgName = bgMap[item.type] ?: @"cell_bg_default";
        cell.imageView.image = [UIImage imageNamed:bgName];
        cell.textLabel.text = @"";
        cell.detailTextLabel.text = @"";
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

@end

/*
 下单(所有认证项已完成 点击产品详情页底部申请按钮调用)
 请求方式 POST from-data 表单提交
 请求地址 "/Alicia/tarts":
 场景 获取跳转位置 跳转之前关闭当前产品详情页 不能出现h5返回又返回到产品详情页 需要返回到首页
 请求参数 【重要】产品详情下发的有对应字段
 名称    类型    可选    注释
 "patiently":    string    否    订单号
 "team":    string    否    金额
 "twowomen":    string    否    借款期限
 "shaggier":    string    否    期限类型
 返回结果
 {
   "awkward": {
       "toteach": "http://47.89.212.157:8080/#/confirmOfLoanV3?orderId=432006&productId=2"  //跳转的地址
   },
   "modest": "0",
   "patted": "success"
 }
 
 */
